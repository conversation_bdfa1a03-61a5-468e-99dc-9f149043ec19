
# A.17.3 RT Image IOD Module Table

**DICOM PS3.3 2025c - Information Object Definitions**

**Navigation:** [PS3.3](PS3.3.html) > [Composite Information Object Definitions (Normative)](chapter_A.html) > [RT Image IOD](sect_A.17.html) > RT Image IOD Module Table

Table A.17.3-1 specifies the Modules of the [RT Image IOD](sect_A.17.html).

## Table A.17.3-1. RT Image IOD Modules

| IE | Module | Reference | Usage |
|----|---------|-----------|-------|
| Patient | Patient | [C.7.1.1](sect_C.7.html#sect_C.7.1.1) | M |
| | Clinical Trial Subject | [C.7.1.3](sect_C.7.html#sect_C.7.1.3) | U |
| Study | General Study | [C.7.2.1](sect_C.7.2.html#sect_C.7.2.1) | M |
| | Patient Study | [C.7.2.2](sect_C.7.2.2.html) | U |
| | Clinical Trial Study | [C.7.2.3](sect_C.7.2.3.html) | U |
| Series | RT Series | [C.8.8.1](sect_C.8.8.html#sect_C.8.8.1) | M |
| | Clinical Trial Series | [C.7.3.2](sect_C.7.3.2.html) | U |
| Frame of Reference | Frame of Reference | [C.7.4.1](sect_C.7.4.html#sect_C.7.4.1) | U |
| Equipment | General Equipment | [C.7.5.1](sect_C.7.5.html#sect_C.7.5.1) | M |
| Acquisition | General Acquisition | [C.7.10.1](sect_C.7.10.html#sect_C.7.10.1) | M |
| Image | General Image | [C.7.6.1](sect_C.7.6.html#sect_C.7.6.1) | M |
| | General Reference | [C.12.4](sect_C.12.4.html) | U |
| | Image Pixel | [C.7.6.3](sect_C.7.6.3.html) | M |
| | Contrast/Bolus | [C.7.6.4](sect_C.7.6.4.html) | C - Required if contrast media was used in this image. |
| | Cine | [C.7.6.5](sect_C.7.6.5.html) | C - Required if Multi-frame Image is a cine image. |
| | Multi-frame | [C.7.6.6](sect_C.7.6.6.html) | C - Required if pixel data is multi-frame data. |
| | Device | [C.7.6.12](sect_C.7.6.12.html) | U |
| | RT Image | [C.8.8.2](sect_C.8.8.2.html) | M |
| | Modality LUT | [C.11.1](sect_C.11.html#sect_C.11.1) | U |
| | VOI LUT | [C.11.2](sect_C.11.2.html) | U |
| | Approval | [C.8.8.16](sect_C.8.8.16.html) | U |
| | SOP Common | [C.12.1](sect_C.12.html#sect_C.12.1) | M |
| | Common Instance Reference | [C.12.2](sect_C.12.2.html) | U |
| | Frame Extraction | [C.12.3](sect_C.12.3.html) | C - Required if the SOP Instance was created in response to a Frame-Level retrieve request |

## Note

1. The inclusion of the Multi-frame Module allows for the expression of time-dependent image series or multiple exposures of identical beam geometries (i.e., multiple exposure portal images). If a time-dependent series of images (such as port images or DRRs) is represented the [Cine Module](sect_C.7.6.5.html) is used to indicate this. This would subsequently allow analysis of Patient movement during treatment. Multiple exposure images allow individual images of treatment ports and open field ports to be grouped into a single Multi-frame Image.

2. The [Modality LUT Module](sect_C.11.html#sect_C.11.1) has been included to allow the possibility of conversion between portal image pixel values and dose transmitted through the Patient. The [VOI LUT Module](sect_C.11.2.html) has been included to allow the possibility of translation between stored pixel values (after the Modality LUT has been applied if specified) and display levels.

3. The [Curve Module (Retired)](sect_C.10.2.html) and [Audio Module (Retired)](sect_C.10.3.html) were previously included in the Image IE for this IOD but have been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf).

4. The [General Equipment Module](sect_C.7.5.html#sect_C.7.5.1) contains information describing the equipment used to acquire or generate the RT Image (such as a portal imager, conventional simulator or treatment planning system). However, the equipment Attributes in the [RT Image Module](sect_C.8.8.2.html) describe the equipment on which the treatment has been or will be given, typically an electron accelerator.

5. For RT Images that contain no relevant pixel data, such as BEV images without DRR information, Pixel Data (7FE0,0010) should be filled with a sequence of zeros.

6. The [Frame of Reference Module](sect_C.7.4.html#sect_C.7.4.1) has been included to allow the indication of spatial association of two or more RT Image Instances (e.g., where the images have been acquired in the same Frame of Reference, or have been resampled to share the same Frame of Reference). If the Frame of Reference occurs within a SOP Instance within a given Series, then all SOP Instances within that Series will be spatially related. For example, two RT Images may share the same Frame of Reference if they are located on the same physical plane, as determined by the treatment machine Gantry Angle (300A,011E) and source to image plane distance specified by RT Image SID (3002,0026).
